```text
瞳孔生物反馈实验复现指南

================================
一、通用设定 (硬件、环境与软件)
================================

1. 硬件设备:
   - 眼动仪: 实验1A/1B/3使用Tobii TX300 (60 Hz)；实验2 (fMRI) 使用EyeLink 1000 Plus (1000 Hz)。
   - 显示器: Philips 240B7QPJ，分辨率设为 1680 x 1050 像素。
   - 下巴支架: 用于固定头部。

2. 实验环境:
   - 房间: 隔音的屏蔽室（法拉第笼）。
   - 光照: 保持恒定的昏暗灯光。
   - 参与者位置: 坐姿，下巴置于支架，眼距屏幕约65厘米。

3. 软件与编程库:
   - 编程环境: MATLAB。
   - 刺激呈现: Psychtoolbox 3.0.17。
   - 眼动数据采集: Tobii SDK for MATLAB 或 Eyelink 1000 Plus Software。

================================
二、核心界面设计与参数
================================

1. 背景与凝视点:
   - 背景色: 恒定的灰色，RGB值为 (150, 150, 150)。
   - 凝视点: 屏幕中央的绿色点，要求全程注视。

2. 生物反馈圈:
   - 基线圈 (Baseline Circle):
     - 样式: 虚线圈，环绕凝视点。
     - 大小: 代表基线阶段最后5秒的平均瞳孔大小。
   - 实时反馈圈 (Moving Circle):
     - 样式: 实线圈。
     - 行为逻辑 (关键): 
       - 只在要求的方向上变化。
       - “调大 (Up)”任务中，只有瞳孔比基线大时，圈才变大。
       - “调小 (Down)”任务中，只有瞳孔比基线小时，圈才变小。
       - 若瞳孔未朝要求方向变化，圈的大小维持在基线圈的大小。
   - 等光亮度 (Isoluminance) 控制 (关键):
     - 目的: 避免屏幕亮度变化影响瞳孔。
     - 实现: 实时调整反馈圈的线条粗细。直径变大时，线条变细，反之亦然，保证有色像素总数恒定。
     - 亮度计算公式: Y = 0.2126 * R + 0.7152 * G + 0.0722 * B。所有颜色（绿、红、黑）需与灰色背景的Y值相等。

================================
三、实时瞳孔数据处理逻辑 (用于反馈)
================================

1. 数据源: 使用主视眼的瞳孔数据。

2. 伪影去除:
   - 生理范围: 拒绝超出 1.5mm 至 9mm 范围的值。
   - 变化速率: 拒绝变化速度大于 0.0027 mm/s 的数据点。

3. 数据平滑:
   - 将最新采集并处理过的2个瞳孔样本取平均值，再用于更新反馈圈。

================================
四、实验流程详解
================================

--- 实验 1A: 学习瞳孔自我调节 ---

- 目标: 训练参与者学会控制瞳孔。
- 结构: 共3天，每天30个“调大”和30个“调小”试次。

- 单个试次 (Trial) 流程 (总时长31秒):
  1. 指令阶段 (2秒): 屏幕显示任务指令，如 "upregulation"。
  2. 基线阶段 (7秒): 屏幕显示凝视点和虚线基线圈。参与者任务为心算倒数（如每次减4）。
  3. 调制阶段 (15秒): 一个实线圈出现，标志开始。参与者运用心理策略调控瞳孔，屏幕上的反馈圈实时变化。
  4. 事后反馈 (2秒): 
     - 一个有颜色的圈显示平均表现：成功为绿色，失败为红色。
     - 一个黑色的圈显示最大/最小表现。
  5. 休息阶段 (5秒): 屏幕显示 "short break"。

- 无反馈测试 (No-feedback Session):
  - 在第3天训练后进行。
  - 无任何在线或事后反馈。
  - 调制阶段开始由符号变化（如 '=' 变为 'x'）提示。

--- 实验 2: fMRI环境下的瞳孔调节 ---

- 目标: 检测瞳孔调节时的大脑活动。
- 单个试次流程:
  1. 基线阶段 (7.5秒)。
  2. 调制阶段 (15秒): 执行任务，但屏幕上无在线反馈。
  3. 事后反馈 (2.5秒): 提供表现反馈。
  4. 休息阶段 (6-9秒，随机抖动)。
- 设计: 组块设计，如连续4个“Up”试次，再连续4个“Down”试次。

--- 实验 3: 结合听觉Oddball任务 ---

- 目标: 测试瞳孔调节对任务表现的影响。
- 三种条件:
  1. 调大 (Up): 调大瞳孔 + Oddball任务。
  2. 调小 (Down): 调小瞳孔 + Oddball任务。
  3. 控制 (Control): 持续心算（每次减7） + Oddball任务。

- 单个试次流程:
  1. 指令阶段 (2秒): 提示是Up, Down还是Control。
  2. 基线阶段 (4秒): 所有条件下，都进行心算（每次减7）。
  3. 调节+听觉任务阶段 (18秒):
     - 执行心理任务（调瞳孔或继续心算）。
     - 同时，播放8个声音（目标音或标准音），听到目标音需按键。
     - 声音间隔（ISI）在1.8-2.2秒之间随机抖动。
     - 阶段开始提示符为品红色 (magenta)。
  4. 事后反馈 (2秒): 仅在Up和Down条件下提供。
  5. 休息阶段 (2秒)。
```